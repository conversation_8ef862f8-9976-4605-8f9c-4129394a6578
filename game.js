// 我的世界游戏主文件 - 高性能版本
class MinecraftGame {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;

        // 改为程序化生成，不存储所有方块
        this.worldData = new Map(); // 只存储修改过的方块
        this.chunks = new Map(); // 分块管理
        this.visibleChunks = new Set(); // 当前可见的分块

        this.player = null;
        this.controls = null;
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        this.selectedBlock = 'grass';
        this.isPointerLocked = false;

        // 性能优化设置
        this.CHUNK_SIZE = 16;
        this.WORLD_HEIGHT = 32; // 减少高度
        this.RENDER_DISTANCE = 3; // 减少渲染距离
        this.MAX_CHUNKS = 25; // 最大分块数量

        // 噪声生成器用于地形
        this.noiseScale = 0.1;
        this.groundLevel = 8;

        // 方块类型定义
        this.blockTypes = {
            grass: { color: 0x7CB342, name: '草方块' },
            dirt: { color: 0x8D6E63, name: '泥土' },
            stone: { color: 0x757575, name: '石头' },
            wood: { color: 0x8D6E63, name: '木头' },
            leaves: { color: 0x4CAF50, name: '树叶' },
            sand: { color: 0xFDD835, name: '沙子' },
            water: { color: 0x2196F3, name: '水' },
            cobblestone: { color: 0x616161, name: '圆石' }
        };

        // 实例化渲染资源
        this.instancedMeshes = new Map();
        this.instanceMatrices = new Map();
        this.instanceColors = new Map();
        this.maxInstancesPerType = 1000; // 每种方块类型最大实例数

        // 性能监控
        this.lastChunkUpdate = 0;
        this.chunkUpdateInterval = 100; // 100ms更新一次分块

        // 回退渲染系统
        this.useInstancedRendering = true;
        this.fallbackMeshes = new Map(); // 传统网格作为回退

        this.init();
    }
    
    init() {
        try {
            console.log('开始设置渲染器...');
            this.setupRenderer();

            console.log('开始设置场景...');
            this.setupScene();

            console.log('开始设置相机...');
            this.setupCamera();

            console.log('开始设置光照...');
            this.setupLights();

            console.log('初始化共享资源...');
            this.initSharedResources();

            console.log('开始设置控制器...');
            this.setupControls();

            console.log('开始设置事件监听器...');
            this.setupEventListeners();

            console.log('开始生成世界...');
            this.generateWorld();

            console.log('开始动画循环...');
            this.animate();

            console.log('游戏初始化完成！');

            // 隐藏加载界面
            document.getElementById('loading').classList.add('hidden');
        } catch (error) {
            console.error('游戏初始化过程中出错:', error);
            document.getElementById('loading').textContent = '初始化失败：' + error.message;
        }
    }
    
    setupRenderer() {
        const canvas = document.getElementById('canvas');
        this.renderer = new THREE.WebGLRenderer({
            canvas: canvas,
            antialias: false, // 关闭抗锯齿提升性能
            powerPreference: "high-performance"
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x87CEEB); // 天空蓝色

        // 关闭阴影以提升性能
        this.renderer.shadowMap.enabled = false;

        // 启用实例化渲染
        this.renderer.capabilities.isWebGL2 = true;

        // 设置像素比，但限制最大值以提升性能
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    }
    
    setupScene() {
        this.scene = new THREE.Scene();
        // 减少雾的距离以提升性能
        this.scene.fog = new THREE.Fog(0x87CEEB, 20, 80);
    }
    
    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        this.camera.position.set(0, 32, 0);
    }
    
    setupLights() {
        // 只使用环境光，移除阴影以大幅提升性能
        const ambientLight = new THREE.AmbientLight(0x404040, 0.8);
        this.scene.add(ambientLight);

        // 简单的方向光，不投射阴影
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
        directionalLight.position.set(50, 100, 50);
        directionalLight.castShadow = false; // 关闭阴影
        this.scene.add(directionalLight);
    }

    initSharedResources() {
        // 创建实例化渲染的几何体和材质
        const geometry = new THREE.BoxGeometry(1, 1, 1);

        Object.keys(this.blockTypes).forEach(type => {
            // 使用更高性能的基础材质
            const material = new THREE.MeshBasicMaterial({
                color: this.blockTypes[type].color,
                fog: true
            });

            // 创建实例化网格
            const instancedMesh = new THREE.InstancedMesh(
                geometry,
                material,
                this.maxInstancesPerType
            );
            instancedMesh.instanceMatrix.setUsage(THREE.DynamicDrawUsage);
            instancedMesh.count = 0; // 初始实例数为0

            // 确保实例化网格可见
            instancedMesh.frustumCulled = false; // 禁用视锥剔除以确保显示

            this.instancedMeshes.set(type, instancedMesh);
            this.scene.add(instancedMesh);

            // 初始化矩阵和颜色数组
            this.instanceMatrices.set(type, []);
            this.instanceColors.set(type, []);

            console.log(`初始化 ${type} 实例化网格`);
        });

        console.log('实例化渲染资源初始化完成，大幅节省内存！');
    }
    
    setupControls() {
        this.controls = new FirstPersonControls(this.camera, this.renderer.domElement);
    }
    
    setupEventListeners() {
        // 窗口大小调整
        window.addEventListener('resize', () => this.onWindowResize());
        
        // 鼠标事件
        this.renderer.domElement.addEventListener('click', () => this.requestPointerLock());
        this.renderer.domElement.addEventListener('mousedown', (event) => this.onMouseDown(event));
        this.renderer.domElement.addEventListener('mousemove', (event) => this.onMouseMove(event));
        
        // 键盘事件
        document.addEventListener('keydown', (event) => this.onKeyDown(event));

        // 右键菜单禁用
        this.renderer.domElement.addEventListener('contextmenu', (event) => {
            event.preventDefault();
        });
        
        // 指针锁定事件
        document.addEventListener('pointerlockchange', () => this.onPointerLockChange());
        
        // 物品栏点击
        document.querySelectorAll('.inventory-slot').forEach((slot, index) => {
            slot.addEventListener('click', () => this.selectInventorySlot(index));
        });
    }
    
    requestPointerLock() {
        this.renderer.domElement.requestPointerLock();
    }
    
    onPointerLockChange() {
        this.isPointerLocked = document.pointerLockElement === this.renderer.domElement;
        if (this.controls) {
            this.controls.enabled = this.isPointerLocked;
        }
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    onMouseDown(event) {
        if (!this.isPointerLocked) return;
        
        if (event.button === 0) { // 左键 - 破坏方块
            this.destroyBlock();
        } else if (event.button === 2) { // 右键 - 放置方块
            this.placeBlock();
        }
    }
    
    onMouseMove(event) {
        if (!this.isPointerLocked) return;
        
        this.mouse.x = 0;
        this.mouse.y = 0;
    }
    
    onKeyDown(event) {
        switch(event.code) {
            case 'Escape':
                document.exitPointerLock();
                break;
            case 'KeyF':
                this.toggleFullscreen();
                break;
            case 'KeyR':
                this.resetWorld();
                break;
            case 'KeyP':
                this.saveWorld();
                break;
            case 'KeyL':
                this.loadWorld();
                break;
            case 'Digit1':
            case 'Digit2':
            case 'Digit3':
            case 'Digit4':
            case 'Digit5':
            case 'Digit6':
            case 'Digit7':
            case 'Digit8':
                const slotIndex = parseInt(event.code.replace('Digit', '')) - 1;
                this.selectInventorySlot(slotIndex);
                break;
        }
    }
    
    selectInventorySlot(index) {
        const slots = document.querySelectorAll('.inventory-slot');
        if (index >= 0 && index < slots.length) {
            // 移除所有活动状态
            slots.forEach(slot => slot.classList.remove('active'));
            // 设置新的活动状态
            slots[index].classList.add('active');
            // 更新选中的方块类型
            this.selectedBlock = slots[index].dataset.block;
        }
    }
    
    generateWorld() {
        console.log('初始化程序化世界生成...');

        // 简化：直接创建一些测试方块来确保渲染工作
        console.log('创建测试方块...');

        // 创建一个简单的平台
        for (let x = -5; x <= 5; x++) {
            for (let z = -5; z <= 5; z++) {
                for (let y = 0; y <= 3; y++) {
                    let blockType = 'stone';
                    if (y === 3) blockType = 'grass';
                    else if (y > 1) blockType = 'dirt';

                    // 直接添加到修改数据中
                    this.worldData.set(`${x},${y},${z}`, blockType);
                }
            }
        }

        console.log(`创建了 ${this.worldData.size} 个测试方块`);

        // 生成玩家周围的分块
        this.updateVisibleChunks();

        console.log('程序化世界生成系统已启动');
    }

    // 程序化生成地形高度
    getTerrainHeight(x, z) {
        // 简单的噪声函数模拟
        const noise1 = Math.sin(x * this.noiseScale) * Math.cos(z * this.noiseScale);
        const noise2 = Math.sin(x * this.noiseScale * 2) * Math.cos(z * this.noiseScale * 2) * 0.5;
        return Math.floor(this.groundLevel + noise1 * 3 + noise2 * 2);
    }

    // 程序化获取方块类型
    getBlockTypeAt(x, y, z) {
        // 确保地面以下有方块
        if (y < 0) return 'stone'; // 基岩层

        const terrainHeight = this.getTerrainHeight(x, z);

        if (y > terrainHeight) return null; // 空气
        if (y === terrainHeight && terrainHeight > 2) return 'grass';
        if (y > terrainHeight - 3 && terrainHeight > 2) return 'dirt';
        return 'stone';
    }

    // 检查是否有方块（程序化 + 修改数据）
    hasBlockAt(x, y, z) {
        const key = `${x},${y},${z}`;

        // 首先检查修改过的方块
        if (this.worldData.has(key)) {
            return this.worldData.get(key) !== null;
        }

        // 然后检查程序化生成的方块
        return this.getBlockTypeAt(x, y, z) !== null;
    }

    // 获取方块类型
    getBlockAt(x, y, z) {
        const key = `${x},${y},${z}`;

        // 首先检查修改过的方块
        if (this.worldData.has(key)) {
            return this.worldData.get(key);
        }

        // 然后返回程序化生成的方块
        return this.getBlockTypeAt(x, y, z);
    }

    // 添加方块到修改数据中
    addBlock(x, y, z, type) {
        const key = `${x},${y},${z}`;
        this.worldData.set(key, type);

        // 标记相关分块需要更新
        const chunkX = Math.floor(x / this.CHUNK_SIZE);
        const chunkZ = Math.floor(z / this.CHUNK_SIZE);
        const chunkKey = `${chunkX},${chunkZ}`;

        if (this.chunks.has(chunkKey)) {
            this.chunks.get(chunkKey).needsUpdate = true;
        }
    }

    // 移除方块
    removeBlock(x, y, z) {
        const key = `${x},${y},${z}`;
        this.worldData.set(key, null); // 标记为已删除

        // 标记相关分块需要更新
        const chunkX = Math.floor(x / this.CHUNK_SIZE);
        const chunkZ = Math.floor(z / this.CHUNK_SIZE);
        const chunkKey = `${chunkX},${chunkZ}`;

        if (this.chunks.has(chunkKey)) {
            this.chunks.get(chunkKey).needsUpdate = true;
        }
    }

    // 兼容性方法
    getBlock(x, y, z) {
        return this.hasBlockAt(x, y, z) ? { userData: { type: this.getBlockAt(x, y, z) } } : null;
    }

    // 分块管理系统
    updateVisibleChunks() {
        const playerPos = this.camera.position;
        const playerChunkX = Math.floor(playerPos.x / this.CHUNK_SIZE);
        const playerChunkZ = Math.floor(playerPos.z / this.CHUNK_SIZE);

        const newVisibleChunks = new Set();

        // 计算需要加载的分块
        for (let dx = -this.RENDER_DISTANCE; dx <= this.RENDER_DISTANCE; dx++) {
            for (let dz = -this.RENDER_DISTANCE; dz <= this.RENDER_DISTANCE; dz++) {
                const chunkX = playerChunkX + dx;
                const chunkZ = playerChunkZ + dz;
                const chunkKey = `${chunkX},${chunkZ}`;

                newVisibleChunks.add(chunkKey);

                // 如果分块不存在，创建它
                if (!this.chunks.has(chunkKey)) {
                    this.createChunk(chunkX, chunkZ);
                }
            }
        }

        // 卸载不再可见的分块
        for (const chunkKey of this.visibleChunks) {
            if (!newVisibleChunks.has(chunkKey)) {
                this.unloadChunk(chunkKey);
            }
        }

        this.visibleChunks = newVisibleChunks;

        // 更新需要重新生成的分块
        for (const chunkKey of this.visibleChunks) {
            const chunk = this.chunks.get(chunkKey);
            if (chunk && chunk.needsUpdate) {
                this.updateChunk(chunkKey);
            }
        }
    }

    createChunk(chunkX, chunkZ) {
        const chunkKey = `${chunkX},${chunkZ}`;

        // 限制最大分块数量
        if (this.chunks.size >= this.MAX_CHUNKS) {
            // 移除最远的分块
            const playerPos = this.camera.position;
            let farthestChunk = null;
            let maxDistance = 0;

            for (const [key, chunk] of this.chunks) {
                const [cx, cz] = key.split(',').map(Number);
                const distance = Math.abs(cx - Math.floor(playerPos.x / this.CHUNK_SIZE)) +
                               Math.abs(cz - Math.floor(playerPos.z / this.CHUNK_SIZE));
                if (distance > maxDistance) {
                    maxDistance = distance;
                    farthestChunk = key;
                }
            }

            if (farthestChunk) {
                this.unloadChunk(farthestChunk);
            }
        }

        const chunk = {
            x: chunkX,
            z: chunkZ,
            needsUpdate: true,
            blockCounts: new Map() // 记录每种方块类型的数量
        };

        this.chunks.set(chunkKey, chunk);
        this.updateChunk(chunkKey);
    }

    updateChunk(chunkKey) {
        const chunk = this.chunks.get(chunkKey);
        if (!chunk) return;

        const chunkX = chunk.x;
        const chunkZ = chunk.z;

        console.log(`更新分块 ${chunkKey}`);

        // 重新构建整个世界的实例化数据（简化版本）
        this.rebuildAllInstances();

        chunk.needsUpdate = false;
    }

    // 重新构建所有实例化数据
    rebuildAllInstances() {
        console.log('开始重建实例化数据...');

        // 清除所有现有实例
        for (const [blockType, instancedMesh] of this.instancedMeshes) {
            instancedMesh.count = 0;
            this.instanceMatrices.get(blockType).length = 0;
        }

        // 收集所有方块（包括修改的和程序化生成的）
        const allBlocksByType = new Map();

        // 首先添加修改过的方块
        for (const [key, blockType] of this.worldData) {
            if (blockType) { // 不是null（已删除）
                const [x, y, z] = key.split(',').map(Number);
                if (!allBlocksByType.has(blockType)) {
                    allBlocksByType.set(blockType, []);
                }
                allBlocksByType.get(blockType).push({ x, y, z });
            }
        }

        // 然后添加可见分块中的程序化方块
        for (const chunkKey of this.visibleChunks) {
            const chunk = this.chunks.get(chunkKey);
            if (!chunk) continue;

            const chunkX = chunk.x;
            const chunkZ = chunk.z;

            for (let x = chunkX * this.CHUNK_SIZE; x < (chunkX + 1) * this.CHUNK_SIZE; x++) {
                for (let z = chunkZ * this.CHUNK_SIZE; z < (chunkZ + 1) * this.CHUNK_SIZE; z++) {
                    for (let y = 0; y < this.WORLD_HEIGHT; y++) {
                        const key = `${x},${y},${z}`;

                        // 跳过已经在修改数据中的方块
                        if (this.worldData.has(key)) continue;

                        const blockType = this.getBlockTypeAt(x, y, z);
                        if (blockType) {
                            if (!allBlocksByType.has(blockType)) {
                                allBlocksByType.set(blockType, []);
                            }
                            allBlocksByType.get(blockType).push({ x, y, z });
                        }
                    }
                }
            }
        }

        console.log(`收集到的方块类型: ${Array.from(allBlocksByType.keys()).join(', ')}`);

        // 更新所有实例化网格
        for (const [blockType, positions] of allBlocksByType) {
            const instancedMesh = this.instancedMeshes.get(blockType);
            const matrices = this.instanceMatrices.get(blockType);

            if (!instancedMesh || !matrices) {
                console.warn(`找不到 ${blockType} 的实例化网格`);
                continue;
            }

            // 清空并重新填充矩阵数组
            matrices.length = 0;

            positions.forEach(pos => {
                const matrix = new THREE.Matrix4();
                matrix.setPosition(pos.x, pos.y, pos.z);
                matrices.push(matrix);
            });

            // 更新实例数量和矩阵
            instancedMesh.count = Math.min(matrices.length, this.maxInstancesPerType);

            for (let i = 0; i < instancedMesh.count; i++) {
                instancedMesh.setMatrixAt(i, matrices[i]);
            }
            instancedMesh.instanceMatrix.needsUpdate = true;

            console.log(`${blockType} 方块数量: ${instancedMesh.count}/${positions.length}`);
        }

        console.log(`总共重建了 ${allBlocksByType.size} 种方块类型`);

        // 如果没有方块，使用回退方案
        if (allBlocksByType.size === 0) {
            console.log('没有找到方块，尝试回退方案...');
            this.useInstancedRendering = false;
            this.rebuildWithFallback();
        }
    }

    // 回退渲染方案 - 使用传统的单个网格
    rebuildWithFallback() {
        console.log('使用传统网格渲染作为回退方案');

        // 清除现有的回退网格
        for (const meshes of this.fallbackMeshes.values()) {
            meshes.forEach(mesh => this.scene.remove(mesh));
        }
        this.fallbackMeshes.clear();

        // 创建共享几何体和材质
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        const materials = new Map();

        Object.keys(this.blockTypes).forEach(type => {
            materials.set(type, new THREE.MeshBasicMaterial({
                color: this.blockTypes[type].color,
                fog: true
            }));
        });

        // 为每个可见分块创建方块
        for (const chunkKey of this.visibleChunks) {
            const chunk = this.chunks.get(chunkKey);
            if (!chunk) continue;

            const chunkX = chunk.x;
            const chunkZ = chunk.z;

            for (let x = chunkX * this.CHUNK_SIZE; x < (chunkX + 1) * this.CHUNK_SIZE; x++) {
                for (let z = chunkZ * this.CHUNK_SIZE; z < (chunkZ + 1) * this.CHUNK_SIZE; z++) {
                    for (let y = 0; y < this.WORLD_HEIGHT; y++) {
                        const blockType = this.getBlockAt(x, y, z);
                        if (blockType) {
                            const material = materials.get(blockType);
                            const mesh = new THREE.Mesh(geometry, material);
                            mesh.position.set(x, y, z);
                            mesh.userData = { type: blockType, x: x, y: y, z: z };

                            this.scene.add(mesh);

                            if (!this.fallbackMeshes.has(blockType)) {
                                this.fallbackMeshes.set(blockType, []);
                            }
                            this.fallbackMeshes.get(blockType).push(mesh);
                        }
                    }
                }
            }
        }

        console.log('回退渲染完成');
    }

    unloadChunk(chunkKey) {
        const chunk = this.chunks.get(chunkKey);
        if (!chunk) return;

        // 从实例化网格中移除这个分块的方块
        for (const [blockType, count] of chunk.blockCounts) {
            const matrices = this.instanceMatrices.get(blockType);
            const instancedMesh = this.instancedMeshes.get(blockType);

            if (matrices && instancedMesh) {
                // 简单的移除策略：重新构建所有其他分块
                // 这里可以优化为更精确的移除
                matrices.length = Math.max(0, matrices.length - count);
                instancedMesh.count = matrices.length;
                instancedMesh.instanceMatrix.needsUpdate = true;
            }
        }

        this.chunks.delete(chunkKey);
        this.visibleChunks.delete(chunkKey);
    }

    generateTree(x, groundY, z) {
        const trunkHeight = 4 + Math.floor(Math.random() * 3);

        // 生成树干
        for (let y = 0; y < trunkHeight; y++) {
            this.addBlock(x, groundY + y, z, 'wood');
        }

        // 生成树叶
        const leavesY = groundY + trunkHeight;
        for (let dx = -2; dx <= 2; dx++) {
            for (let dz = -2; dz <= 2; dz++) {
                for (let dy = 0; dy < 3; dy++) {
                    if (dx === 0 && dz === 0 && dy === 0) continue;
                    if (Math.abs(dx) === 2 && Math.abs(dz) === 2) continue;
                    if (Math.random() < 0.8) {
                        this.addBlock(x + dx, leavesY + dy, z + dz, 'leaves');
                    }
                }
            }
        }
    }

    destroyBlock() {
        // 使用优化的射线检测
        const hitResult = this.raycastBlocks();

        if (hitResult && hitResult.distance <= 5) {
            this.removeBlock(hitResult.x, hitResult.y, hitResult.z);

            // 添加破坏效果
            this.createBreakEffect(new THREE.Vector3(hitResult.x, hitResult.y, hitResult.z));

            // 如果使用回退渲染，需要移除对应的网格
            if (!this.useInstancedRendering) {
                this.removeFallbackMesh(hitResult.x, hitResult.y, hitResult.z);
            }
        }
    }

    // 移除回退渲染的网格
    removeFallbackMesh(x, y, z) {
        for (const [blockType, meshes] of this.fallbackMeshes) {
            for (let i = meshes.length - 1; i >= 0; i--) {
                const mesh = meshes[i];
                if (mesh.userData.x === x && mesh.userData.y === y && mesh.userData.z === z) {
                    this.scene.remove(mesh);
                    meshes.splice(i, 1);
                    return;
                }
            }
        }
    }

    // 优化的射线检测 - 直接检测方块而不是网格对象
    raycastBlocks() {
        const camera = this.camera;
        const direction = new THREE.Vector3();
        camera.getWorldDirection(direction);

        const start = camera.position.clone();
        const maxDistance = 5;
        const step = 0.1;

        for (let distance = 0; distance < maxDistance; distance += step) {
            const point = start.clone().add(direction.clone().multiplyScalar(distance));
            const x = Math.floor(point.x);
            const y = Math.floor(point.y);
            const z = Math.floor(point.z);

            if (this.hasBlockAt(x, y, z)) {
                return {
                    x: x,
                    y: y,
                    z: z,
                    distance: distance,
                    type: this.getBlockAt(x, y, z)
                };
            }
        }

        return null;
    }

    placeBlock() {
        // 使用优化的射线检测
        const hitResult = this.raycastBlocks();

        if (hitResult && hitResult.distance <= 5) {
            // 计算放置位置（在击中方块的相邻位置）
            const camera = this.camera;
            const direction = new THREE.Vector3();
            camera.getWorldDirection(direction);

            // 简化的放置逻辑：在击中方块的上方放置
            let placeX = hitResult.x;
            let placeY = hitResult.y + 1;
            let placeZ = hitResult.z;

            // 如果上方被占用，尝试其他方向
            if (this.hasBlockAt(placeX, placeY, placeZ)) {
                // 尝试前后左右
                const offsets = [[1,0,0], [-1,0,0], [0,0,1], [0,0,-1], [0,-1,0]];
                let placed = false;

                for (const [dx, dy, dz] of offsets) {
                    const testX = hitResult.x + dx;
                    const testY = hitResult.y + dy;
                    const testZ = hitResult.z + dz;

                    if (!this.hasBlockAt(testX, testY, testZ)) {
                        placeX = testX;
                        placeY = testY;
                        placeZ = testZ;
                        placed = true;
                        break;
                    }
                }

                if (!placed) return; // 无法放置
            }

            // 检查是否与玩家位置冲突
            const playerPos = this.camera.position;
            const distance = Math.abs(playerPos.x - placeX) +
                           Math.abs(playerPos.y - placeY) +
                           Math.abs(playerPos.z - placeZ);

            if (distance > 1.5) { // 简化的碰撞检测
                this.addBlock(placeX, placeY, placeZ, this.selectedBlock);

                // 添加放置效果
                this.createPlaceEffect(new THREE.Vector3(placeX, placeY, placeZ));
            }
        }
    }

    createBreakEffect(position) {
        // 创建简单的破坏粒子效果
        const particleCount = 10;
        const particles = new THREE.Group();

        for (let i = 0; i < particleCount; i++) {
            const geometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
            const material = new THREE.MeshBasicMaterial({
                color: Math.random() * 0xffffff,
                transparent: true,
                opacity: 0.8
            });
            const particle = new THREE.Mesh(geometry, material);

            particle.position.copy(position);
            particle.position.add(new THREE.Vector3(
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2
            ));

            particles.add(particle);
        }

        this.scene.add(particles);

        // 动画粒子
        const animate = () => {
            particles.children.forEach(particle => {
                particle.position.y -= 0.05;
                particle.material.opacity -= 0.02;
            });

            if (particles.children[0].material.opacity > 0) {
                requestAnimationFrame(animate);
            } else {
                this.scene.remove(particles);
                particles.children.forEach(particle => {
                    particle.geometry.dispose();
                    particle.material.dispose();
                });
            }
        };
        animate();
    }

    createPlaceEffect(position) {
        // 创建简单的放置效果
        const geometry = new THREE.BoxGeometry(1.1, 1.1, 1.1);
        const material = new THREE.MeshBasicMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.3,
            wireframe: true
        });
        const effect = new THREE.Mesh(geometry, material);
        effect.position.copy(position);

        this.scene.add(effect);

        // 动画效果
        const animate = () => {
            effect.material.opacity -= 0.05;
            effect.scale.multiplyScalar(1.02);

            if (effect.material.opacity > 0) {
                requestAnimationFrame(animate);
            } else {
                this.scene.remove(effect);
                effect.geometry.dispose();
                effect.material.dispose();
            }
        };
        animate();
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());

        if (this.controls) {
            this.controls.update();
        }

        // 定期更新可见分块
        const now = performance.now();
        if (now - this.lastChunkUpdate > this.chunkUpdateInterval) {
            this.updateVisibleChunks();
            this.lastChunkUpdate = now;
        }

        this.updateUI();
        this.renderer.render(this.scene, this.camera);
    }
    
    updateUI() {
        // 更新位置信息
        const pos = this.camera.position;
        document.getElementById('position').textContent =
            `${Math.floor(pos.x)}, ${Math.floor(pos.y)}, ${Math.floor(pos.z)}`;

        // 更新分块和内存信息
        const chunkCount = this.chunks.size;
        const modifiedBlocks = this.worldData.size;
        const memoryInfo = this.getMemoryInfo();
        document.getElementById('blockCount').textContent =
            `分块: ${chunkCount}, 修改: ${modifiedBlocks} (${memoryInfo})`;

        // 更新FPS
        this.updateFPS();

        // 更新准星高亮（降低频率以提升性能）
        if (this.frameCount % 3 === 0) { // 每3帧更新一次
            this.updateCrosshairHighlight();
        }
    }

    updateFPS() {
        if (!this.lastTime) this.lastTime = performance.now();
        if (!this.frameCount) this.frameCount = 0;

        this.frameCount++;
        const currentTime = performance.now();

        if (currentTime - this.lastTime >= 1000) {
            const fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
            document.getElementById('fps').textContent = fps;
            this.frameCount = 0;
            this.lastTime = currentTime;
        }
    }

    getMemoryInfo() {
        // 获取内存使用信息
        if (performance.memory) {
            const used = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
            const total = Math.round(performance.memory.totalJSHeapSize / 1024 / 1024);
            return `${used}/${total}MB`;
        }
        return '内存信息不可用';
    }

    updateCrosshairHighlight() {
        // 移除之前的高亮
        if (this.highlightedBlock) {
            this.scene.remove(this.highlightedBlock);
            this.highlightedBlock.geometry.dispose();
            this.highlightedBlock.material.dispose();
            this.highlightedBlock = null;
        }

        // 使用优化的射线检测
        const hitResult = this.raycastBlocks();

        if (hitResult && hitResult.distance <= 5) {
            // 创建高亮边框
            const geometry = new THREE.BoxGeometry(1.01, 1.01, 1.01);
            const material = new THREE.MeshBasicMaterial({
                color: 0xffffff,
                transparent: true,
                opacity: 0.3,
                wireframe: true
            });

            this.highlightedBlock = new THREE.Mesh(geometry, material);
            this.highlightedBlock.position.set(hitResult.x, hitResult.y, hitResult.z);
            this.scene.add(this.highlightedBlock);
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    resetWorld() {
        if (confirm('确定要重置世界吗？这将删除所有方块！')) {
            // 清除所有方块
            this.world.forEach(block => {
                this.scene.remove(block);
                block.geometry.dispose();
                block.material.dispose();
            });
            this.world.clear();

            // 重新生成世界
            this.generateWorld();

            // 重置玩家位置
            this.camera.position.set(0, 32, 0);
        }
    }

    saveWorld() {
        try {
            const worldData = {
                blocks: [],
                playerPosition: {
                    x: this.camera.position.x,
                    y: this.camera.position.y,
                    z: this.camera.position.z
                },
                playerRotation: {
                    x: this.camera.rotation.x,
                    y: this.camera.rotation.y,
                    z: this.camera.rotation.z
                }
            };

            // 保存所有方块数据
            this.world.forEach((block, key) => {
                const [x, y, z] = key.split(',').map(Number);
                worldData.blocks.push({
                    x: x,
                    y: y,
                    z: z,
                    type: block.userData.type
                });
            });

            // 保存到本地存储
            localStorage.setItem('minecraftWorld', JSON.stringify(worldData));

            // 显示保存成功消息
            this.showMessage('世界已保存！');
        } catch (error) {
            console.error('保存世界失败:', error);
            this.showMessage('保存失败！');
        }
    }

    loadWorld() {
        try {
            const savedData = localStorage.getItem('minecraftWorld');
            if (!savedData) {
                this.showMessage('没有找到保存的世界！');
                return;
            }

            const worldData = JSON.parse(savedData);

            // 清除当前世界
            this.world.forEach(block => {
                this.scene.remove(block);
                block.geometry.dispose();
                block.material.dispose();
            });
            this.world.clear();

            // 加载方块
            worldData.blocks.forEach(blockData => {
                this.addBlock(blockData.x, blockData.y, blockData.z, blockData.type);
            });

            // 恢复玩家位置
            if (worldData.playerPosition) {
                this.camera.position.set(
                    worldData.playerPosition.x,
                    worldData.playerPosition.y,
                    worldData.playerPosition.z
                );
            }

            if (worldData.playerRotation) {
                this.camera.rotation.set(
                    worldData.playerRotation.x,
                    worldData.playerRotation.y,
                    worldData.playerRotation.z
                );
            }

            this.showMessage('世界已加载！');
        } catch (error) {
            console.error('加载世界失败:', error);
            this.showMessage('加载失败！');
        }
    }

    showMessage(text) {
        // 创建消息元素
        const message = document.createElement('div');
        message.textContent = text;
        message.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 5px;
            font-size: 18px;
            z-index: 1000;
            pointer-events: none;
        `;

        document.body.appendChild(message);

        // 3秒后移除消息
        setTimeout(() => {
            document.body.removeChild(message);
        }, 3000);
    }
}

// 第一人称控制器类
class FirstPersonControls {
    constructor(camera, domElement) {
        this.camera = camera;
        this.domElement = domElement;
        this.enabled = false;
        
        this.moveSpeed = 10;
        this.jumpSpeed = 15;
        this.gravity = -30;
        this.velocity = new THREE.Vector3();
        this.direction = new THREE.Vector3();
        
        this.keys = {
            forward: false,
            backward: false,
            left: false,
            right: false,
            jump: false
        };
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        document.addEventListener('keydown', (event) => this.onKeyDown(event));
        document.addEventListener('keyup', (event) => this.onKeyUp(event));
        document.addEventListener('mousemove', (event) => this.onMouseMove(event));
    }
    
    onKeyDown(event) {
        switch(event.code) {
            case 'KeyW': this.keys.forward = true; break;
            case 'KeyS': this.keys.backward = true; break;
            case 'KeyA': this.keys.left = true; break;
            case 'KeyD': this.keys.right = true; break;
            case 'Space': this.keys.jump = true; break;
        }
    }
    
    onKeyUp(event) {
        switch(event.code) {
            case 'KeyW': this.keys.forward = false; break;
            case 'KeyS': this.keys.backward = false; break;
            case 'KeyA': this.keys.left = false; break;
            case 'KeyD': this.keys.right = false; break;
            case 'Space': this.keys.jump = false; break;
        }
    }
    
    onMouseMove(event) {
        if (!this.enabled) return;
        
        const sensitivity = 0.002;
        this.camera.rotation.y -= event.movementX * sensitivity;
        this.camera.rotation.x -= event.movementY * sensitivity;
        this.camera.rotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.camera.rotation.x));
    }
    
    update() {
        if (!this.enabled) return;

        const delta = 0.016; // 假设60fps

        // 重置方向
        this.direction.set(0, 0, 0);

        // 计算移动方向
        if (this.keys.forward) this.direction.z -= 1;
        if (this.keys.backward) this.direction.z += 1;
        if (this.keys.left) this.direction.x -= 1;
        if (this.keys.right) this.direction.x += 1;

        // 标准化方向向量
        if (this.direction.length() > 0) {
            this.direction.normalize();

            // 应用相机旋转
            const quaternion = new THREE.Quaternion();
            quaternion.setFromEuler(new THREE.Euler(0, this.camera.rotation.y, 0));
            this.direction.applyQuaternion(quaternion);

            // 更新水平速度
            this.velocity.x = this.direction.x * this.moveSpeed;
            this.velocity.z = this.direction.z * this.moveSpeed;
        } else {
            // 如果没有输入，逐渐减速
            this.velocity.x *= 0.8;
            this.velocity.z *= 0.8;
        }

        // 跳跃逻辑
        const groundLevel = this.getGroundLevel(this.camera.position.x, this.camera.position.z);
        const isOnGround = this.camera.position.y <= groundLevel + 1.8;

        if (this.keys.jump && isOnGround) {
            this.velocity.y = this.jumpSpeed;
        }

        // 应用重力
        this.velocity.y += this.gravity * delta;

        // 更新位置
        const newPosition = this.camera.position.clone();
        newPosition.add(this.velocity.clone().multiplyScalar(delta));

        // 碰撞检测
        if (this.checkCollision(newPosition)) {
            // 如果有碰撞，只更新Y轴
            this.camera.position.y = Math.max(newPosition.y, groundLevel + 1.8);
            if (this.camera.position.y === groundLevel + 1.8) {
                this.velocity.y = 0;
            }
        } else {
            this.camera.position.copy(newPosition);
            // 地面碰撞检测
            if (this.camera.position.y < groundLevel + 1.8) {
                this.camera.position.y = groundLevel + 1.8;
                this.velocity.y = 0;
            }
        }
    }

    getGroundLevel(x, z) {
        // 优化的地面高度检测
        const floorX = Math.floor(x);
        const floorZ = Math.floor(z);

        // 从程序化生成的地面开始检测
        const baseHeight = game ? game.getTerrainHeight(floorX, floorZ) : 8;

        // 向上和向下搜索
        for (let y = baseHeight + 5; y >= 0; y--) {
            if (game && game.hasBlockAt(floorX, y, floorZ)) {
                return y;
            }
        }
        return baseHeight;
    }

    checkCollision(position) {
        // 优化的碰撞检测
        const playerRadius = 0.3;

        // 只检查玩家脚部和头部位置
        const checkPositions = [
            { x: position.x, y: position.y - 0.1, z: position.z }, // 脚部
            { x: position.x, y: position.y + 0.9, z: position.z }  // 头部
        ];

        for (const checkPos of checkPositions) {
            const blockX = Math.floor(checkPos.x);
            const blockY = Math.floor(checkPos.y);
            const blockZ = Math.floor(checkPos.z);

            if (game && game.hasBlockAt(blockX, blockY, blockZ)) {
                return true;
            }
        }

        return false;
    }
}

// 全局游戏变量
let game = null;

// 启动游戏
window.addEventListener('load', () => {
    console.log('页面加载完成，开始初始化游戏...');

    // 检查Three.js是否加载
    if (typeof THREE === 'undefined') {
        console.error('Three.js 库未加载！');
        document.getElementById('loading').textContent = '错误：Three.js 库加载失败！';
        return;
    }

    console.log('Three.js 库已加载，版本:', THREE.REVISION);

    try {
        game = new MinecraftGame();
        console.log('游戏初始化成功！');
    } catch (error) {
        console.error('游戏初始化失败:', error);
        document.getElementById('loading').textContent = '游戏初始化失败：' + error.message;
    }
});
